# 后台管理权限配置说明

## 概述
本文档说明了后台管理功能的权限配置，确保只有超级管理员（role=3）可以访问后台管理功能。

## 用户角色定义
根据项目需求，用户角色定义如下：
- 0: 临时用户
- 1: 普通用户
- 2: 管理员
- 3: 超级管理员
- 4: 主任

## 权限配置更改

### 1. 常量定义 (constants.js)
添加了用户角色常量定义：
```javascript
export const USER_ROLE = Object.freeze({
    TEMP_USER: 0,        // 临时用户
    NORMAL_USER: 1,      // 普通用户
    ADMIN: 2,            // 管理员
    SUPER_ADMIN: 3,      // 超级管理员
    DIRECTOR: 4,         // 主任
    0: 'TEMP_USER',
    1: 'NORMAL_USER',
    2: 'ADMIN',
    3: 'SUPER_ADMIN',
    4: 'DIRECTOR'
});
```

### 2. 路由权限管理器 (RoutePermissionManager.js)
更新了后台管理路由的权限配置：
```javascript
'/backgroundManage': { roles: [2, 3], permissions: ['admin'] }
```

新增了静态方法用于路由权限检查：
- `isProtectedRoute(routePath)`: 检查路由是否需要权限控制
- `getRequiredRoles(routePath)`: 获取路由所需的角色权限
- `getCurrentUserRole()`: 获取当前用户角色
- `checkRouteAccess(routePath)`: 检查当前用户是否有访问权限（内部自动获取用户角色）

### 3. 基础权限管理器 (BasePermissionManager.js)
更新了超级管理员检查逻辑：
```javascript
isSuperAdmin() {
    return this.getUserRole() === 3; // 超级管理员角色为3
}
```

### 4. 主权限管理器 (PermissionManager.js)
同步更新了超级管理员检查逻辑。

### 5. 左侧边栏组件 (leftSideBar.vue)
- 更新了菜单项显示权限检查
- 在openBackgroundManage方法中添加了权限验证
- 只有超级管理员才能看到和访问后台管理菜单

### 6. 路由守卫 (router/index.js)
重构了路由守卫的权限检查逻辑，使用权限管理器进行统一管理：
```javascript
// 路由权限检查
if (RoutePermissionManager.isProtectedRoute(to.path)) {
    // 检查权限（用户角色在权限管理器内部获取）
    if (!RoutePermissionManager.checkRouteAccess(to.path)) {
        const userRole = RoutePermissionManager.getCurrentUserRole();
        const requiredRoles = RoutePermissionManager.getRequiredRoles(to.path);
        console.warn('用户权限不足，无法访问路由', {
            route: to.path,
            userRole,
            requiredRoles
        });
        next('/main');
        return;
    }
}
```

这种方式的优势：
- 不需要硬编码具体路由路径
- 用户角色获取封装在权限管理器内部，路由守卫更简洁
- 支持多个权限路由的统一管理
- 易于扩展新的权限路由

### 7. 后台管理页面 (backgroundManage.vue)
- 在组件mounted生命周期中添加权限检查
- 更新了管理员登录验证逻辑，只允许超级管理员登录

## 权限检查层级

权限检查在多个层级进行，确保安全性：

1. **菜单显示层级**: 只有超级管理员才能看到后台管理菜单项
2. **菜单点击层级**: 点击后台管理菜单时进行权限验证
3. **路由守卫层级**: 在路由跳转时进行权限检查
4. **组件加载层级**: 在后台管理组件加载时进行权限验证
5. **功能使用层级**: 在后台管理功能使用时进行权限检查

## 测试方法

可以使用提供的测试文件进行权限验证：
```javascript
// 在浏览器控制台中运行
window.testPermission.runAllTests();
```

## 如何添加新的权限路由

要添加新的权限路由，只需要在 `RoutePermissionManager.js` 中进行配置：

### 步骤1: 添加到受保护路由列表
在 `isProtectedRoute` 方法中添加新路由：
```javascript
static isProtectedRoute(routePath) {
    const protectedRoutes = [
        '/backgroundManage',
        '/systemManage',      // 新增的系统管理路由
        '/userManage',        // 新增的用户管理路由
    ];

    return protectedRoutes.some(route => routePath === route || routePath.startsWith(route + '/'));
}
```

### 步骤2: 配置权限要求
在 `getRequiredRoles` 方法中配置权限：
```javascript
static getRequiredRoles(routePath) {
    const routeRoleMap = {
        '/backgroundManage': [2, 3],  // 管理员和超级管理员
        '/systemManage': [3],         // 只有超级管理员
        '/userManage': [2, 3],        // 管理员和超级管理员
    };
    // ... 其余代码保持不变
}
```

### 步骤3: 路由守卫自动生效
配置完成后，路由守卫会自动应用新的权限检查，无需修改路由守卫代码。

## 注意事项

1. 确保用户的role字段正确设置（2为管理员，3为超级管理员）
2. 新增权限路由只需要在 `RoutePermissionManager.js` 中配置，路由守卫会自动应用
3. 权限检查是多层级的，确保在所有层级都进行了正确的验证
4. 建议在生产环境中进行充分的权限测试

## 相关文件

- `src/module/ultrasync_pc/lib/constants.js` - 角色常量定义
- `src/module/ultrasync_pc/lib/permission/RoutePermissionManager.js` - 路由权限配置
- `src/module/ultrasync_pc/lib/permission/BasePermissionManager.js` - 基础权限管理
- `src/module/ultrasync_pc/lib/permission/PermissionManager.js` - 主权限管理器
- `src/module/ultrasync_pc/components/leftSideBar.vue` - 左侧边栏组件
- `src/module/ultrasync_pc/router/index.js` - 路由配置和守卫
- `src/module/ultrasync_pc/pages/backgroundManage.vue` - 后台管理页面
- `src/module/ultrasync_pc/lib/permission/test-permission.js` - 权限测试工具
