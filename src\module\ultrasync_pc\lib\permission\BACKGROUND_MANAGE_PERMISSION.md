# 后台管理权限配置说明

## 概述
本文档说明了后台管理功能的权限配置，确保只有超级管理员（role=3）可以访问后台管理功能。

## 用户角色定义
根据项目需求，用户角色定义如下：
- 0: 临时用户
- 1: 普通用户  
- 2: 管理员
- 3: 超级管理员
- 4: 主任

## 权限配置更改

### 1. 常量定义 (constants.js)
添加了用户角色常量定义：
```javascript
export const USER_ROLE = Object.freeze({
    TEMP_USER: 0,        // 临时用户
    NORMAL_USER: 1,      // 普通用户
    ADMIN: 2,            // 管理员
    SUPER_ADMIN: 3,      // 超级管理员
    DIRECTOR: 4,         // 主任
    0: 'TEMP_USER',
    1: 'NORMAL_USER',
    2: 'ADMIN',
    3: 'SUPER_ADMIN',
    4: 'DIRECTOR'
});
```

### 2. 路由权限管理器 (RoutePermissionManager.js)
更新了后台管理路由的权限配置：
```javascript
'/backgroundManage': { roles: [3], permissions: ['super_admin'] }
```

### 3. 基础权限管理器 (BasePermissionManager.js)
更新了超级管理员检查逻辑：
```javascript
isSuperAdmin() {
    return this.getUserRole() === 3; // 超级管理员角色为3
}
```

### 4. 主权限管理器 (PermissionManager.js)
同步更新了超级管理员检查逻辑。

### 5. 左侧边栏组件 (leftSideBar.vue)
- 更新了菜单项显示权限检查
- 在openBackgroundManage方法中添加了权限验证
- 只有超级管理员才能看到和访问后台管理菜单

### 6. 路由守卫 (router/index.js)
在路由守卫中添加了后台管理路由的权限检查：
```javascript
if(to.path === '/backgroundManage') {
    const userRole = window.vm && window.vm.$store && window.vm.$store.state.user ? window.vm.$store.state.user.role : 0;
    if(userRole !== 3) {
        console.warn('用户权限不足，无法访问后台管理', { userRole, requiredRole: 3 });
        next('/main');
        return;
    }
}
```

### 7. 后台管理页面 (backgroundManage.vue)
- 在组件mounted生命周期中添加权限检查
- 更新了管理员登录验证逻辑，只允许超级管理员登录

## 权限检查层级

权限检查在多个层级进行，确保安全性：

1. **菜单显示层级**: 只有超级管理员才能看到后台管理菜单项
2. **菜单点击层级**: 点击后台管理菜单时进行权限验证
3. **路由守卫层级**: 在路由跳转时进行权限检查
4. **组件加载层级**: 在后台管理组件加载时进行权限验证
5. **功能使用层级**: 在后台管理功能使用时进行权限检查

## 测试方法

可以使用提供的测试文件进行权限验证：
```javascript
// 在浏览器控制台中运行
window.testPermission.runAllTests();
```

## 注意事项

1. 确保用户的role字段正确设置为3才能访问后台管理
2. 如果需要修改权限规则，需要同时更新多个文件中的权限检查逻辑
3. 权限检查是多层级的，确保在所有层级都进行了正确的验证
4. 建议在生产环境中进行充分的权限测试

## 相关文件

- `src/module/ultrasync_pc/lib/constants.js` - 角色常量定义
- `src/module/ultrasync_pc/lib/permission/RoutePermissionManager.js` - 路由权限配置
- `src/module/ultrasync_pc/lib/permission/BasePermissionManager.js` - 基础权限管理
- `src/module/ultrasync_pc/lib/permission/PermissionManager.js` - 主权限管理器
- `src/module/ultrasync_pc/components/leftSideBar.vue` - 左侧边栏组件
- `src/module/ultrasync_pc/router/index.js` - 路由配置和守卫
- `src/module/ultrasync_pc/pages/backgroundManage.vue` - 后台管理页面
- `src/module/ultrasync_pc/lib/permission/test-permission.js` - 权限测试工具
