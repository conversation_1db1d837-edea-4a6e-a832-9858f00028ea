<template>
    <div>
        <CommonDialog
            class="background_manage"
            :title="$t('background_manage_title')"
            :show.sync="visible"
            :close-on-click-modal="false"
            width="1200px"
            :modal="false"
            @closed="back"
            :footShow="false"
        >
            <el-card v-if="!isLogined" class="login_card">
                <div slot="header">
                    <span>{{ $t("admin_login_title") }}</span>
                </div>
                <div class="body" @keyup.enter="submit">
                    <el-form :model="login_form" label-width="0px" :rules="login_rules" ref="login_form">
                        <el-form-item label="" prop="name">
                            <el-input
                                v-model="login_form.name"
                                :disabled="true"
                                :placeholder="$t('register_account')"
                                autocomplete="new-password"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="" prop="pwd">
                            <el-input
                                type="password"
                                v-model="login_form.pwd"
                                :placeholder="$t('register_password')"
                                autocomplete="new-password"
                            ></el-input>
                        </el-form-item>
                    </el-form>
                    <el-button @click="submit" v-loading="logining" class="login_btn" type="primary">{{
                        $t("login_title")
                    }}</el-button>
                </div>
            </el-card>
            <template v-else>
                <div class="container">
                    <el-tabs v-model="activeName" @tab-click="handleChange">
                        <el-tab-pane :label="$t('admin_tab_approve')" name="approve" v-loading="loadingApprove">
                            <div class="search_wrapper clearfix">
                                <el-button @click="passAllRegister" type="primary" size="small" class="fl">{{
                                    $t("admin_batch_pass")
                                }}</el-button>
                                <el-input
                                    class="fr"
                                    v-model="registerSearch"
                                    @input="filterRegister"
                                    :placeholder="`${$t('admin_login_name')}、${$t('admin_register_date')}、${$t(
                                        'nickname'
                                    )}、${$t('register_mobile')}`"
                                ></el-input>
                                <span class="fr">{{ $t("search") }}：</span>
                            </div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>
                                            <el-checkbox
                                                v-model="registerSelectAll"
                                                @change="registerChangeAll"
                                            ></el-checkbox>
                                        </th>
                                        <th>{{ $t("admin_login_name") }}</th>
                                        <th>{{ $t("admin_register_date") }}</th>
                                        <th>{{ $t("nickname") }}</th>
                                        <th>{{ $t("register_mobile") }}</th>
                                        <th>{{ $t("register_email") }}</th>
                                        <th>{{ $t("admin_approve") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(register, index) in registerShowList" :key="index">
                                        <td>
                                            <el-checkbox
                                                v-model="register.checked"
                                                @change="changeRegisterItem"
                                            ></el-checkbox>
                                        </td>
                                        <td>{{ register.name }}</td>
                                        <td>{{ formatTime(register.creation_ts) }}</td>
                                        <td>{{ register.nickname }}</td>
                                        <td>
                                            {{
                                                register.mobile_phone
                                                    ? `+${register.international_code}${register.mobile_phone}`
                                                    : ""
                                            }}
                                        </td>
                                        <td>{{ register.email }}</td>
                                        <td>
                                            <el-button @click="passRegister(register)" type="primary" size="mini">{{
                                                $t("admin_pass")
                                            }}</el-button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <el-pagination
                                background
                                layout="prev, pager, next, total"
                                :page-size="pageSize"
                                :total="registerFilterList.length"
                                :current-page.sync="registerPage"
                                @current-change="changeRegisterPage"
                            ></el-pagination>
                        </el-tab-pane>
                        <el-tab-pane
                            v-if="false"
                            :label="$t('admin_tab_user_manage')"
                            name="user"
                            v-loading="loadingUser"
                        >
                            <div class="search_wrapper clearfix">
                                <el-input
                                    v-model="userSearch"
                                    @input="filterUser"
                                    class="fr"
                                    :placeholder="`${$t('admin_login_name')}、${$t('admin_register_date')}、${$t(
                                        'nickname'
                                    )}、${$t('register_mobile')}`"
                                ></el-input>
                                <span class="fr">{{ $t("search") }}：</span>
                            </div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>{{ $t("admin_login_name") }}</th>
                                        <th>{{ $t("admin_register_date") }}</th>
                                        <th>{{ $t("nickname") }}</th>
                                        <th>{{ $t("register_mobile") }}</th>
                                        <th>{{ $t("register_email") }}</th>
                                        <th>{{ $t("scan_room_hospital") }}</th>
                                        <th>{{ $t("admin_approver_nickname") }}</th>
                                        <th>{{ $t("admin_referee_nickname") }}</th>
                                        <th>{{ $t("admin_is_manager") }}</th>
                                        <th>{{ $t("admin_is_director") }}</th>
                                        <th>{{ $t("admin_setting_password") }}</th>
                                        <th>{{ $t("admin_activation_and_disabled") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(user, index) in userShowList" :key="index">
                                        <td>{{ user.name }}</td>
                                        <td>{{ formatTime(user.creation_ts) }}</td>
                                        <td>{{ user.nickname }}</td>
                                        <td>
                                            {{
                                                user.mobile_phone
                                                    ? `+${user.international_code}${user.mobile_phone}`
                                                    : ""
                                            }}
                                        </td>
                                        <td>{{ user.email }}</td>
                                        <td>
                                            {{ user.hospital_name || "" }}
                                            <el-button @click="changeHospital(user)" type="primary" size="mini">{{
                                                $t("modify_btn_text")
                                            }}</el-button>
                                        </td>
                                        <td>{{ user.approver_nickname || "" }}</td>
                                        <td>{{ user.referee_nickname || "" }}</td>
                                        <td>
                                            <template v-if="user.role == 2">
                                                <span>{{ $t("confirm_button_text") }}</span>
                                                <el-button
                                                    v-if="isSpAdmin"
                                                    @click="changeRole(user, 2)"
                                                    type="primary"
                                                    size="mini"
                                                    >{{ $t("admin_revoking_permissions") }}</el-button
                                                >
                                            </template>
                                            <template v-else>
                                                <span>{{ $t("cancel_button_text") }}</span>
                                                <el-button
                                                    v-if="isSpAdmin"
                                                    @click="changeRole(user, 1)"
                                                    type="primary"
                                                    size="mini"
                                                    >{{ $t("admin_granting_permissions") }}</el-button
                                                >
                                            </template>
                                        </td>
                                        <td>
                                            <template v-if="user.role == 4">
                                                <span>{{ $t("confirm_button_text") }}</span>
                                                <el-button @click="changeRole(user, 3)" type="primary" size="mini">{{
                                                    $t("admin_revoking_permissions")
                                                }}</el-button>
                                            </template>
                                            <template v-else-if="user.role == 2">
                                                {{ $t("cancel_button_text") }}
                                            </template>
                                            <template v-else>
                                                <span>{{ $t("cancel_button_text") }}</span>
                                                <el-button @click="changeRole(user, 4)" type="primary" size="mini">{{
                                                    $t("admin_granting_permissions")
                                                }}</el-button>
                                            </template>
                                        </td>
                                        <td>
                                            <el-button type="primary" size="mini" @click="openPasswordModal(user)">{{
                                                $t("modify_password_text")
                                            }}</el-button>
                                        </td>
                                        <td>
                                            <el-button
                                                v-if="user.verification == 1"
                                                @click="changeUserStatus(user, 3)"
                                                type="danger"
                                                size="mini"
                                                >{{ $t("admin_disabled") }}</el-button
                                            >
                                            <el-button
                                                v-if="user.verification == 3"
                                                @click="changeUserStatus(user, 1)"
                                                type="primary"
                                                size="mini"
                                                >{{ $t("admin_activation") }}</el-button
                                            >
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <el-pagination
                                background
                                layout="prev, pager, next, total"
                                :page-size="pageSize"
                                :total="userFilterList.length"
                                :current-page.sync="userPage"
                                @current-change="changeUserPage"
                            ></el-pagination>
                        </el-tab-pane>
                        <el-tab-pane
                            v-loading="loadingHospital"
                            :label="$t('admin_tab_hospital_manage')"
                            name="hospital"
                        >
                            <div class="search_wrapper clearfix">
                                <el-button @click="addHospital" type="primary" size="small" class="fl">{{
                                    $t("admin_add")
                                }}</el-button>
                                <el-input
                                    v-model="hospitalSearch"
                                    @input="filterHospital"
                                    :placeholder="$t('admin_hospital_name')"
                                    class="fr"
                                ></el-input>
                                <span class="fr">{{ $t("search") }}：</span>
                            </div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>{{ $t("admin_hospital_name") }}</th>
                                        <th>{{ $t("location") }}</th>
                                        <th>{{ $t("modify_btn_text") }}</th>
                                        <th>{{ $t("action_delete_text") }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(hospital, index) in hospitalShowList" :key="index">
                                        <td>{{ hospital.hospital_name }}</td>
                                        <td>{{ hospital.region }}</td>
                                        <td>
                                            <el-button @click="editHospital(hospital)" type="primary" size="mini">{{
                                                $t("modify_btn_text")
                                            }}</el-button>
                                        </td>
                                        <td>
                                            <el-button @click="deleteHospital(hospital)" type="danger" size="mini">{{
                                                $t("action_delete_text")
                                            }}</el-button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <el-pagination
                                background
                                layout="prev, pager, next, total"
                                :page-size="pageSize"
                                :total="hospitalFilterList.length"
                                :current-page.sync="hospitalPage"
                                @current-change="changeHospitalPage"
                            ></el-pagination>
                        </el-tab-pane>
                        <el-tab-pane :label="$t('multicenter_approve')" name="multicenter">
                            <multicenter-approve></multicenter-approve>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <el-dialog
                    class="reset_user_password"
                    :title="$t('modify_password_text')"
                    :visible="isShowPasswordModal"
                    :close-on-click-modal="true"
                    width="400px"
                    :modal="false"
                    :before-close="closeResetPassword"
                >
                    <div class="reset_item clearfix">
                        <span>{{ $t("admin_login_name") }}：</span>
                        <span>{{ tempUser.name }}</span>
                    </div>
                    <div class="reset_item clearfix">
                        <span>{{ $t("nickname") }}：</span>
                        <span>{{ tempUser.nickname }}</span>
                    </div>
                    <div class="reset_item clearfix">
                        <span>{{ $t("admin_user_id") }}：</span>
                        <span>{{ tempUser.id }}</span>
                    </div>
                    <div class="reset_item clearfix">
                        <span>{{ $t("modify_new_password_label") }}：</span>
                        <el-input v-model="password" maxlength="16"></el-input>
                    </div>
                    <el-button @click="resetPasswordSubmit" v-loading="reseting" class="submit_btn" type="primary">{{
                        $t("submit_btn")
                    }}</el-button>
                </el-dialog>
                <el-dialog
                    class="hospital_manage_modal"
                    :title="hospitalModalTitle"
                    :visible="isShowHospitalModal"
                    :close-on-click-modal="true"
                    :append-to-body="true"
                    width="400px"
                    :modal="false"
                    :before-close="closeHospitalModal"
                >
                    <div class="item clearfix" @keyup.enter="hospitalSubmit">
                        <span>{{ $t("admin_hospital_name") }}：</span>
                        <el-input v-model="hospital_name" maxlength="32"></el-input>
                    </div>
                    <div class="item clearfix">
                        <span>{{ $t("location") }}：</span>
                        <el-input :disabled="true" v-model="hospital_region"></el-input>
                        <i class="iconfont iconGroup" @click="isShowMapPicker = true"></i>
                    </div>
                    <template v-if="tempHospital">
                        <div class="sub_hospitals" v-loading="loadingAssociation">
                            <div class="sub_title clearfix">
                                <p class="fl">{{ $t("association_hospitals") }}：</p>
                                <el-dropdown class="fr" trigger="click" @command="addAssociation">
                                    <el-button
                                        :disabled="filter_association_hospitals.length == 0"
                                        type="primary"
                                        size="mini"
                                        >{{ $t("add_association") }}</el-button
                                    >
                                    <el-dropdown-menu slot="dropdown" class="hospital_list">
                                        <el-input
                                            v-model="hospital_filter_keyword"
                                            :placeholder="$t('filter_hospital')"
                                            @input="changeHospitalFilterList"
                                            @blur="handleFilterBlur"
                                        ></el-input>
                                        <el-dropdown-item
                                            v-for="(item, index) of filtered_hospitals"
                                            :command="item"
                                            :key="index"
                                            >{{ item.hospital_name }}</el-dropdown-item
                                        >
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </div>
                            <div class="tree_wrap">
                                <vue-scroll>
                                    <el-tree :data="association_hospitals" :props="associationProps" default-expand-all>
                                        <span class="custom_tree_node" slot-scope="{ node }">
                                            <span>{{ node.label }}</span>
                                            <span @click="deleteAssociation(node)" class="delete">{{
                                                $t("delete")
                                            }}</span>
                                        </span>
                                    </el-tree>
                                </vue-scroll>
                            </div>
                        </div>
                    </template>
                    <el-button
                        @click="hospitalSubmit"
                        v-loading="hospitalSubmiting"
                        class="submit_btn"
                        type="primary"
                        >{{ $t("submit_btn") }}</el-button
                    >
                </el-dialog>
                <el-dialog
                    class="change_hospital_modal"
                    :title="$t('modify_btn_text')"
                    :visible="isShowChangeHospital"
                    :close-on-click-modal="true"
                    width="400px"
                    :modal="false"
                    :before-close="closeChangeHospital"
                >
                    <div class="item clearfix">
                        <span>{{ $t("admin_hospital_name") }}：</span>
                        <el-select v-model="currentHospital">
                            <el-option
                                v-for="item in hospitalList"
                                :label="item.hospital_name"
                                :value="item.id"
                                :key="item.id"
                            ></el-option>
                        </el-select>
                    </div>
                    <el-button
                        @click="changeHospitalSubmit"
                        v-loading="changeHospitalSubmiting"
                        class="submit_btn"
                        type="primary"
                        >{{ $t("submit_btn") }}</el-button
                    >
                </el-dialog>
                <template v-if="isShowMapPicker">
                    <map-picker :show.sync="isShowMapPicker" :callback="pickerCb"></map-picker>
                </template>
            </template>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import service from "../service/service";
import mapPicker from "../components/mapPicker";
import multicenterApprove from "../components/genericMulticenter/multicenterApprove";
import CommonDialog from "../MRComponents/commonDialog.vue";
import { passwordStrengthRegExp } from "@/common/regExpMapping.js";
export default {
    mixins: [base],
    name: "BackgroundManage",
    model: {
        prop: "value",
        event: "input",
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: { mapPicker, multicenterApprove, CommonDialog },
    data() {
        var validatePwd = (rule, value, callback) => {
            if (value === "") {
                callback(new Error(this.$t("login_password_empty")));
            } else if (!passwordStrengthRegExp.test(value)) {
                callback(new Error(this.$t("password_rule_incorrect")));
            } else {
                callback();
            }
        };
        return {
            isLogined: false,
            logining: false,
            loadingApprove: false,
            loadingUser: false,
            loadingHospital: false,
            activeName: "approve",
            pageSize: 10,
            filerTimer: null,
            registerShowList: [],
            registerFilterList: [],
            registerAllData: [],
            registerPage: 1,
            registerSelectAll: false,
            registerSearch: "",
            userShowList: [],
            userFilterList: [],
            userAllData: [],
            userPage: 1,
            userSearch: "",
            password: "",
            isShowPasswordModal: false,
            tempUser: {},
            reseting: false,
            hospitalShowList: [],
            hospitalFilterList: [],
            hospitalAllData: [],
            hospitalPage: 1,
            hospitalSearch: "",
            hospital_name: "",
            hospital_region: "",
            hospital_center: [],
            isShowMapPicker: false,
            isShowHospitalModal: false,
            isShowChangeHospital: false,
            hospitalModalTitle: "",
            hospitalSubmiting: false,
            tempHospital: null,
            login_form: {
                name: "",
                pwd: "",
            },
            login_rules: {
                name: [
                    {
                        required: true,
                        message: this.$t("login_name_length_not_correct"),
                        trigger: "blur",
                    },
                    { min: 4, max: 16, message: this.$t("login_name_length_not_correct"), trigger: "blur" },
                ],
                pwd: [
                    {
                        validator: validatePwd,
                        trigger: "blur",
                    },
                ],
            },
            isSpAdmin: false,
            currentHospital: null,
            changeHospitalSubmiting: false,
            currentUser: {},
            association_hospitals: [],
            filter_association_hospitals: [],
            filtered_hospitals: [],
            loadingAssociation: false,
            associationProps: {
                children: "sub_hospitals",
                label: "hospital_name",
            },
            hospital_filter_keyword: "",
        };
    },
    computed: {
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit("input", val);
            },
        },
        hospitalList() {
            return this.dynamicGlobalParams.hospitals;
        },
    },
    mounted() {
        this.$nextTick(() => {
            // 检查用户权限：只有超级管理员可以访问
            if (this.user.role !== 3 && this.user.role !== 2) {
                this.$message.error('您没有权限访问后台管理');
                this.$emit('input', false); // 关闭弹窗
                return;
            }

            this.login_form.name = this.user.username;
            this.isSpAdmin = this.user.role == 3 ? true : false;
        });
    },
    methods: {
        submit() {
            this.$refs["login_form"].validate((valid) => {
                if (valid) {
                    this.logining = true;
                    service.AdminLogin({ user: this.login_form }).then((res) => {
                        if (res.data.role) {
                            if (res.data.role == 2 || res.data.role == 3) {
                                this.isLogined = true;
                                this.getRegisterData();
                            } else {
                                this.$message.error("只有超级管理员可以访问后台管理");
                            }
                        } else {
                            if (res.data.error_code == "login_fail_account_incorrect") {
                                this.$message.error(this.$t("modify_password_fail_account_incorrect"));
                            } else if (res.data.error_code == "login_fail_password_incorrect") {
                                this.$message.error(this.$t("password_error_text"));
                            } else {
                                this.$message.error(res.data.error_code);
                            }
                        }
                        this.logining = false;
                    });
                }
            });
        },
        handleChange(tab) {
            if (tab.name == "approve") {
                this.getRegisterData();
            } else if (tab.name == "user") {
                this.getUserData();
            } else if (tab.name == "hospital") {
                this.getHospitalData();
            }
        },
        getRegisterData() {
            this.loadingApprove = true;
            this.registerSearch = "";
            this.registerShowList = [];
            window.main_screen.controller.emit("AdminRegisterApplyQuery", {}, (data) => {
                if (data.error_code) {
                    this.$message.error(data.error_code);
                } else {
                    for (let item of data.list) {
                        item.checked = false;
                    }
                    let arr = data.list.slice();
                    arr.sort(function (a, b) {
                        return Date.parse(a.creation_ts) > Date.parse(b.creation_ts) ? -1 : 1;
                    });
                    this.registerAllData = arr;
                    this.registerFilterList = arr;
                    this.changeRegisterPage(1);
                }
                this.loadingApprove = false;
            });
        },
        filterRegister(event, pageNum = 1) {
            clearTimeout(this.filerTimer);
            this.filerTimer = setTimeout(() => {
                this.registerFilterList = [];
                for (let item of this.registerAllData) {
                    if (
                        item.name.indexOf(this.registerSearch) != -1 ||
                        item.nickname.indexOf(this.registerSearch) != -1 ||
                        item.creation_ts.indexOf(this.registerSearch) != -1 ||
                        (item.mobile_phone && item.mobile_phone.indexOf(this.registerSearch) != -1)
                    ) {
                        this.registerFilterList.push(item);
                    }
                }
                this.changeRegisterPage(pageNum);
            }, 300);
        },
        changeRegisterPage(pageNum) {
            if ((pageNum - 1) * this.pageSize >= this.registerFilterList.length) {
                //当展示数据少于跳转页面数量时，向后退一页
                pageNum--;
            }
            this.registerPage = pageNum;
            this.registerShowList = this.registerFilterList.slice(
                (pageNum - 1) * this.pageSize,
                pageNum * this.pageSize
            );
            for (let item of this.registerShowList) {
                item.checked = false;
            }
            this.changeRegisterItem();
        },
        registerChangeAll() {
            for (let item of this.registerShowList) {
                item.checked = this.registerSelectAll;
            }
        },
        changeRegisterItem() {
            let tag = true;
            for (let item of this.registerShowList) {
                if (!item.checked) {
                    tag = false;
                    break;
                }
            }
            this.registerSelectAll = this.registerShowList.length && tag;
        },
        passRegister(register) {
            let user_id = register.id;
            this.passAction([user_id]);
        },
        passAllRegister() {
            let list = [];
            for (let item of this.registerShowList) {
                if (item.checked) {
                    list.push(item.id);
                }
            }
            if (list.length == 0) {
                this.$message.error(this.$t("admin_pass_empty"));
                return;
            }
            this.passAction(list);
        },
        passAction(list) {
            this.loadingApprove = true;
            window.main_screen.controller.emit(
                "AdminRegisterApplyVerify",
                {
                    ad_id: this.user.id,
                    list: list,
                },
                (data) => {
                    if (data.error_code) {
                        this.$message.error(data.error_code);
                    } else {
                        //删除前端数据
                        for (let user_id of data.list) {
                            for (let i = 0; i < this.registerAllData.length; ) {
                                let item = this.registerAllData[i];
                                if (item.id == user_id) {
                                    this.registerAllData.splice(i, 1);
                                } else {
                                    i++;
                                }
                            }
                        }
                        this.filterRegister({}, this.registerPage);
                        this.loadingApprove = false;
                    }
                }
            );
        },
        getUserData() {
            this.loadingUser = true;
            this.userSearch = "";
            this.userShowList = [];
            window.main_screen.controller.emit("AdminUserManageQuery", {}, (data) => {
                if (data.error_code) {
                    this.$message.error(data.error_code);
                } else {
                    let temp = [];
                    for (let item of data.list) {
                        if (item.id != this.user.uid) {
                            temp.push(item);
                        }
                    }
                    let arr = temp.slice();
                    arr.sort(function (a, b) {
                        return Date.parse(a.creation_ts) > Date.parse(b.creation_ts) ? -1 : 1;
                    });
                    this.userAllData = arr;
                    this.userFilterList = arr;
                    this.changeUserPage(1);
                }
                this.loadingUser = false;
            });
        },
        filterUser(event, pageNum = 1) {
            clearTimeout(this.filerTimer);
            this.filerTimer = setTimeout(() => {
                this.userFilterList = [];
                for (let item of this.userAllData) {
                    if (
                        item.name.indexOf(this.userSearch) != -1 ||
                        item.nickname.indexOf(this.userSearch) != -1 ||
                        item.creation_ts.indexOf(this.userSearch) != -1 ||
                        (item.mobile_phone && item.mobile_phone.indexOf(this.userSearch) != -1)
                    ) {
                        this.userFilterList.push(item);
                    }
                }
                this.changeUserPage(pageNum);
            }, 300);
        },
        changeUserPage(pageNum) {
            if ((pageNum - 1) * this.pageSize >= this.userFilterList.length) {
                //当展示数据少于跳转页面数量时，向后退一页
                pageNum--;
            }
            this.userPage = pageNum;
            this.userShowList = this.userFilterList.slice((pageNum - 1) * this.pageSize, pageNum * this.pageSize);
        },
        changeRole(user, type) {
            let msg = "";
            let role = 1;
            if (type == 1) {
                msg = this.$t("admin_granting_permissions_tip");
                role = 2;
            } else if (type == 2) {
                msg = this.$t("admin_revoking_permissions_tip");
            } else if (type == 3) {
                msg = this.$t("admin_relief_director_tip");
            } else if (type == 4) {
                msg = this.$t("admin_setup_director_tip");
                role = 4;
            }
            this.$confirm(msg, this.$t("tip_title"), {
                confirmButtonText: this.$t("confirm_button_text"),
                cancelButtonText: this.$t("cancel_button_text"),
                type: "warning",
            }).then(() => {
                this.loadingUser = true;
                window.main_screen.controller.emit(
                    "AdminUserManageSetUserRole",
                    {
                        user_id: user.id,
                        role: role,
                    },
                    (data) => {
                        if (data.error_code) {
                            this.$message.error(data.error_code);
                        } else {
                            for (let item of this.userShowList) {
                                if (item.id == data.user_id) {
                                    item.role = data.role;
                                    break;
                                }
                            }
                        }
                        this.loadingUser = false;
                    }
                );
            });
        },
        changeUserStatus(user, status) {
            let msg = "";
            if (status == 1) {
                msg = this.$t("admin_active_user_tip");
            } else {
                msg = this.$t("admin_forbidden_user_tip");
            }
            this.$confirm(msg, this.$t("tip_title"), {
                confirmButtonText: this.$t("confirm_button_text"),
                cancelButtonText: this.$t("cancel_button_text"),
                type: "warning",
            }).then(() => {
                this.loadingUser = true;
                window.main_screen.controller.emit(
                    "AdminUserManageSetUserStatus",
                    {
                        user_id: user.id,
                        status: status,
                    },
                    (data) => {
                        if (data.error_code) {
                            this.$message.error(data.error_code);
                        } else {
                            for (let item of this.userShowList) {
                                if (item.id == data.user_id) {
                                    item.verification = data.status;
                                    break;
                                }
                            }
                        }
                        this.loadingUser = false;
                    }
                );
            });
        },
        openPasswordModal(user) {
            this.tempUser = user;
            this.password = "";
            this.isShowPasswordModal = true;
        },
        closeResetPassword() {
            this.isShowPasswordModal = false;
        },
        resetPasswordSubmit() {
            // var passwordRegexp=/^[a-zA-Z0-9~!@#$%^&*()_+`\[\]\\\|\;\':",.\/<>\?\*]{6,16}$/
            // var passwordRegexpEnhanced=/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9~!@#$%^&*()_+`\[\]\\\|\;\':",.\/<>\?\*]{8,16}$/
            if (!passwordStrengthRegExp.test(this.password)) {
                this.$message.error(this.$t("enhanced_password_format_tip"));
                return;
            } else {
                this.reseting = true;
                window.main_screen.controller.emit(
                    "AdminUserManageResetUserPassword",
                    {
                        user_id: this.tempUser.id,
                        pwd: this.password,
                    },
                    (data) => {
                        if (data.error_code) {
                            this.$message.error(data.error_code);
                        } else {
                            this.$message.success(this.$t("update_success_text"));
                            this.isShowPasswordModal = false;
                        }
                        this.reseting = false;
                    }
                );
            }
        },
        getHospitalData() {
            this.loadingHospital = true;
            this.hospitalSearch = "";
            this.hospitalShowList = [];
            window.main_screen.controller.emit("AdminHospitalManageQuery", {}, (data) => {
                if (data.error_code) {
                    this.$message.error(data.error_code);
                } else {
                    let arr = data.list.slice();
                    arr.sort((a, b) => {
                        return a.id > b.id ? -1 : 1;
                    });
                    this.hospitalAllData = arr;
                    this.hospitalFilterList = arr;
                    // this.hospitalAllData=data.list;
                    // this.hospitalFilterList=data.list;
                    this.changeHospitalPage(1);
                }
                this.loadingHospital = false;
            });
        },
        filterHospital(event, pageNum = 1) {
            clearTimeout(this.filerTimer);
            this.filerTimer = setTimeout(() => {
                this.hospitalFilterList = [];
                for (let item of this.hospitalAllData) {
                    if (item.hospital_name.indexOf(this.hospitalSearch) != -1) {
                        this.hospitalFilterList.push(item);
                    }
                }
                this.changeHospitalPage(pageNum);
            }, 300);
        },
        changeHospitalFilterList(keyword) {
            clearTimeout(this.filterTimer2);
            // 防抖处理
            this.filterTimer2 = setTimeout(() => {
                // 当未输入关键字，展示所有医院
                if (!keyword.trim()) {
                    this.filtered_hospitals = this.filter_association_hospitals;
                } else {
                    // 用户有输入，取出带有输入关键字的医院列表
                    this.filtered_hospitals = this.filter_association_hospitals.map((item) => {
                        return item.hospital_name.includes(keyword) && item;
                    });
                }
            }, 300);
        },
        handleFilterBlur() {
            setTimeout(() => {
                this.hospital_filter_keyword = "";
                this.filtered_hospitals = this.filter_association_hospitals;
            }, 300);
        },
        changeHospitalPage(pageNum) {
            if (pageNum == 0) {
                pageNum = 1;
            }
            if ((pageNum - 1) * this.pageSize >= this.hospitalFilterList.length) {
                //当展示数据少于跳转页面数量时，向后退一页
                pageNum--;
            }
            this.hospitalPage = pageNum;
            this.hospitalShowList = this.hospitalFilterList.slice(
                (pageNum - 1) * this.pageSize,
                pageNum * this.pageSize
            );
        },
        addHospital() {
            this.openHospitalModal();
        },
        editHospital(hospital) {
            this.openHospitalModal(hospital);
        },
        openHospitalModal(hospital) {
            if (!hospital) {
                this.tempHospital = null;
                this.hospitalModalTitle = this.$t("admin_add") + this.$t("admin_hospital_name");
                this.hospital_name = "";
                this.hospital_region = "";
            } else {
                this.hospitalModalTitle = this.$t("modify_btn_text") + this.$t("admin_hospital_name");
                this.hospital_name = hospital.hospital_name;
                this.hospital_region = hospital.region;
                this.hospital_center[0] = hospital.longitude;
                this.hospital_center[1] = hospital.latitude;
                this.tempHospital = hospital;
                this.association_hospitals = [];
                this.filter_association_hospitals = [];
                this.loadingAssociation = true;
                window.main_screen.controller.emit(
                    "AdminHospitalManageQueryHospitalAssociation",
                    {
                        hospital_id: hospital.id,
                    },
                    (data) => {
                        hospital.hospital_association_id = data.hospital_association_id;
                        this.association_hospitals = data.sub_hospitals;
                        this.loadingAssociation = false;
                        this.filter_association_hospitals = [].concat(this.hospitalList);
                        this.filterAssociation([hospital]);
                        this.filterAssociation(this.association_hospitals);
                        this.filtered_hospitals = this.filter_association_hospitals;
                    }
                );
            }
            this.isShowHospitalModal = true;
        },
        closeHospitalModal() {
            this.hospital_name = "";
            this.isShowHospitalModal = false;
        },
        closeChangeHospital() {
            this.isShowChangeHospital = false;
            this.currentHospital = null;
        },
        hospitalSubmit() {
            var that = this;
            if (this.hospital_name == "") {
                this.$message.error(this.$t("admin_hospital_name_empty"));
                return;
            }
            this.hospitalSubmiting = true;
            if (!this.tempHospital) {
                //新增
                window.main_screen.controller.emit(
                    "AdminHospitalManageAddHospital",
                    {
                        hospital_name: this.hospital_name,
                        region: this.hospital_region,
                        longitude: this.hospital_center[0],
                        latitude: this.hospital_center[1],
                    },
                    (data) => {
                        if (data.error_code) {
                            this.$message.error(this.$t(data.error_code) || data.error_code);
                        } else {
                            // that.hospitalAllData.push(data);
                            that.hospitalAllData.unshift(data);
                            this.filterHospital({}, that.hospitalPage);

                            this.isShowHospitalModal = false;
                            let hospitals = this.dynamicGlobalParams.hospitals;
                            hospitals.push(data);
                            this.$store.commit("dynamicGlobalParams/updateDynamicGlobalParams", {
                                hospitals: hospitals,
                            });
                        }
                        this.hospitalSubmiting = false;
                    }
                );
            } else {
                //修改医院名称
                window.main_screen.controller.emit(
                    "AdminHospitalManageUpdateHospital",
                    {
                        hospital_name: this.hospital_name,
                        region: this.hospital_region,
                        longitude: this.hospital_center[0],
                        latitude: this.hospital_center[1],
                        id: this.tempHospital.id,
                    },
                    (data) => {
                        if (data.error_code) {
                            this.$message.error(this.$t(data.error_code) || data.error_code);
                        } else {
                            this.tempHospital.hospital_name = data.hospital_name;
                            this.tempHospital.region = data.region;
                            this.tempHospital.longitude = data.longitude;
                            this.tempHospital.latitude = data.latitude;
                            this.isShowHospitalModal = false;
                            let hospitals = this.hospitalList;
                            for (let hospital of hospitals) {
                                if (hospital.id == this.tempHospital.id) {
                                    hospital.hospital_name = this.hospital_name;
                                    hospital.region = this.hospital_region;
                                    hospital.longitude = this.hospital_center[0];
                                    hospital.latitude = this.hospital_center[1];
                                    break;
                                }
                            }
                            this.$store.commit("dynamicGlobalParams/updateDynamicGlobalParams", {
                                hospitals: hospitals,
                            });
                        }
                        this.hospitalSubmiting = false;
                    }
                );
            }
        },
        changeHospitalSubmit() {
            if (!this.currentHospital) {
                return;
            } else {
                this.changeHospitalSubmiting = true;
                window.main_screen.controller.emit(
                    "AdminUserManageSetUserHospital",
                    {
                        user_id: this.currentUser.id,
                        hospital_id: this.currentHospital,
                    },
                    (data) => {
                        if (data.error_code) {
                            if (data.error_code === "hospital_name_repeated") {
                                this.$message.error(this.$t("hospital_name_repeated"));
                            } else {
                                this.$message.error(data.error_code);
                            }
                        } else {
                            this.isShowChangeHospital = false;
                            let hospitals = this.hospitalList;
                            for (let hospital of hospitals) {
                                if (hospital.id == this.currentHospital) {
                                    this.currentUser.hospital_name = hospital.hospital_name;
                                    break;
                                }
                            }
                        }
                        this.changeHospitalSubmiting = false;
                    }
                );
            }
        },
        deleteHospital(hospital) {
            this.$confirm(this.$t("admin_hospital_delete"), this.$t("tip_title"), {
                confirmButtonText: this.$t("confirm_button_text"),
                cancelButtonText: this.$t("cancel_button_text"),
                type: "warning",
            }).then(() => {
                this.loadingHospital = true;
                window.main_screen.controller.emit(
                    "AdminHospitalManageDeleteHospital",
                    {
                        id: hospital.id,
                    },
                    (data) => {
                        if (data.error_code) {
                            this.$message.error(data.error_code);
                        } else {
                            //删除前端数据
                            for (let i = 0; i < this.hospitalAllData.length; ) {
                                let item = this.hospitalAllData[i];
                                if (item.id == data.id) {
                                    this.hospitalAllData.splice(i, 1);
                                    break;
                                } else {
                                    i++;
                                }
                            }
                            let hospitals = this.dynamicGlobalParams.hospitals;
                            for (let index = 0; index < hospitals.length; index++) {
                                let hospital = hospitals[index];
                                if (hospital.id == data.id) {
                                    hospitals.splice(index, 1);
                                    break;
                                }
                            }
                            this.$store.commit("dynamicGlobalParams/updateDynamicGlobalParams", {
                                hospitals: hospitals,
                            });
                            this.filterHospital({}, this.hospitalPage);
                        }
                        this.loadingHospital = false;
                    }
                );
            });
        },
        changeHospital(user) {
            this.currentUser = user;
            let hospital_name = user.hospital_name || "";
            for (let item of this.hospitalList) {
                if (hospital_name == item.hospital_name) {
                    this.currentHospital = item.id;
                    break;
                }
            }
            this.isShowChangeHospital = true;
        },
        pickerCb(region, center) {
            this.hospital_region = region;
            this.hospital_center = center;
        },
        filterAssociation(data) {
            for (let item of data) {
                let id = item.hospital_id || item.id;
                for (let index = 0; index < this.filter_association_hospitals.length; index++) {
                    if (this.filter_association_hospitals[index].id == id) {
                        this.filter_association_hospitals.splice(index, 1);
                        break;
                    }
                }
                if (item.sub_hospitals) {
                    this.filterAssociation(item.sub_hospitals);
                }
            }
        },
        addAssociation(hospital) {
            this.loadingAssociation = true;
            window.main_screen.controller.emit(
                "AdminHospitalManageAddHospitalAssociation",
                {
                    hospital_id: hospital.id,
                    parent_id: this.tempHospital.hospital_association_id,
                },
                (data) => {
                    this.loadingAssociation = false;
                    this.association_hospitals.push(data);
                    this.filterAssociation([hospital]);
                }
            );
        },
        deleteAssociation(node) {
            this.loadingAssociation = true;
            let hospital_association_id = node.data.hospital_association_id;
            window.main_screen.controller.emit(
                "AdminHospitalManageDeleteHospitalAssociation",
                {
                    hospital_association_id: hospital_association_id,
                },
                (data) => {
                    this.loadingAssociation = false;
                    for (let index = 0; index < this.association_hospitals.length; index++) {
                        if (this.association_hospitals[index].hospital_id == node.data.hospital_id) {
                            this.association_hospitals.splice(index, 1);
                            break;
                        }
                    }
                    this.filter_association_hospitals = [].concat(this.hospitalList);
                    this.filterAssociation([this.tempHospital]);
                    this.filterAssociation(this.association_hospitals);
                    this.filtered_hospitals = this.filter_association_hospitals;
                }
            );
        },
        back() {
            // 关闭弹窗
            this.visible = false;
        },
    },
};
</script>
<style lang="scss">
.background_manage {
    .el-dialog {
        height:650px !important;
    }
    .dialog-content {
        display: flex;
        flex-direction: column;
        height: 100%;
        .login_card {
            width:480px;
            margin:100px auto;
            .el-card__header {
                span {
                    color: #58a29f;
                    font-size: 22px;
                }
            }
            .login_btn {
                width: 100%;
                margin: 0 auto 20px;
                display: block;
                height: 50px;
            }
            input {
                padding: 8px 12px;
                font-size: 18px;
                height: 50px;
                box-sizing: border-box;
                letter-spacing: 2px;
                font-weight: 500;
                line-height: 24px;
                color:#333;
            }
            input:-webkit-autofill {
                transition:background-color 999999s ease-in-out 0s;
                -webkit-text-fill-color: #333;
            }
            input:focus {
                border:1px solid #6ab3ad;
            }
        }
        .container {
            height:100%;
            .el-tabs {
                height: 100%;
                display: flex;
                flex-direction: column;
                .el-tabs__content {
                    flex:1;
                    overflow:auto;
                    .search_wrapper {
                        margin-bottom: 10px;
                        .el-input {
                            width:400px;
                        }
                        &>span {
                            line-height:40px;
                        }
                    }
                    th {
                        white-space: nowrap;
                    }
                    th,td {
                        border:1px solid #bbb;
                        font-size: 14px;
                        padding: 6px;
                        text-align: left;
                    }
                    table {
                        color:#333;
                        border:1px solid #bbb;
                        width:100%;
                        border-collapse: collapse;
                        margin-bottom:10px;
                    }
                }
            }
        }
    }
    .reset_user_password{
        .reset_item{
            line-height:40px;
            font-size:16px;
            display:flex;
            &>span{
                word-break: keep-all;
            }
        }
        .submit_btn{
            margin-top:20px;
        }
    }
}
.hospital_manage_modal,.change_hospital_modal{
    .item{
        margin-top:20px;
        line-height:40px;
        font-size:16px;
        display:flex;
        &>span{
            word-break: keep-all;
        }
        .iconGroup{
            font-size: 32px;
            margin-left: 10px;
            cursor: pointer;
            color: #000;
        }
    }
    .sub_hospitals{
        margin-top:20px;
        font-size:16px;
        height:calc(100% - 137px);
        .sub_title{
            margin-bottom:6px;
        }
        .tree_wrap{
            height:calc(100% - 34px);
            padding-bottom:60px;
        }
        .custom_tree_node{
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            padding-right:20px;
            .delete{
                color:#F56C6C;
            }
        }
    }
    .submit_btn{
        position: absolute;
        right: 14px;
        bottom: 14px;
    }
}
.hospital_list{
  max-height:80%;
  overflow:auto;
}
</style>
