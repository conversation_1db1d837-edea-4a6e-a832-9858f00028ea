/**
 * 权限系统测试文件
 * 用于验证后台管理权限是否正确配置
 */

import { USER_ROLE } from '../constants.js';
import permissionManager from './index.js';

/**
 * 测试用户角色常量
 */
export function testUserRoleConstants() {
    console.log('=== 测试用户角色常量 ===');
    console.log('临时用户:', USER_ROLE.TEMP_USER, USER_ROLE[0]);
    console.log('普通用户:', USER_ROLE.NORMAL_USER, USER_ROLE[1]);
    console.log('管理员:', USER_ROLE.ADMIN, USER_ROLE[2]);
    console.log('超级管理员:', USER_ROLE.SUPER_ADMIN, USER_ROLE[3]);
    console.log('主任:', USER_ROLE.DIRECTOR, USER_ROLE[4]);
}

/**
 * 测试权限管理器
 */
export async function testPermissionManager() {
    console.log('=== 测试权限管理器 ===');

    // 测试不同角色的用户
    const testUsers = [
        { uid: '1', role: 0, nickname: '临时用户' },
        { uid: '2', role: 1, nickname: '普通用户' },
        { uid: '3', role: 2, nickname: '管理员' },
        { uid: '4', role: 3, nickname: '超级管理员' },
        { uid: '5', role: 4, nickname: '主任' }
    ];

    for (const user of testUsers) {
        console.log(`\n--- 测试用户: ${user.nickname} (role: ${user.role}) ---`);

        // 初始化权限管理器
        await permissionManager.initialize(user);

        // 测试路由权限
        const hasRoutePermission = permissionManager.checkRoutePermission('/backgroundManage');
        console.log(`后台管理路由权限: ${hasRoutePermission}`);

        // 测试角色检查
        const isAdmin = permissionManager.isAdmin();
        const isSuperAdmin = permissionManager.isSuperAdmin();
        console.log(`是否为管理员: ${isAdmin}`);
        console.log(`是否为超级管理员: ${isSuperAdmin}`);

        // 测试特定权限
        const hasSuperAdminPermission = permissionManager.checkFeaturePermission('super_admin');
        console.log(`超级管理员权限: ${hasSuperAdminPermission}`);
    }
}

/**
 * 模拟路由权限检查
 */
export function simulateRouteCheck(routePath = '/backgroundManage') {
    console.log(`\n=== 模拟路由权限检查 (路由: ${routePath}) ===`);

    // 导入权限管理器进行检查
    import('./RoutePermissionManager.js').then(({ default: RoutePermissionManager }) => {
        const currentUserRole = RoutePermissionManager.getCurrentUserRole();
        const hasAccess = RoutePermissionManager.checkRouteAccess(routePath);
        const requiredRoles = RoutePermissionManager.getRequiredRoles(routePath);

        console.log(`当前用户角色: ${currentUserRole}`);

        if (hasAccess) {
            console.log('✅ 权限验证通过，允许访问');
        } else {
            console.log('❌ 权限不足，无法访问');
            console.log(`需要角色: ${requiredRoles?.join(', ') || '无限制'}`);
            console.log('重定向到: /main');
        }

        return hasAccess;
    });
}

/**
 * 测试不同用户角色的权限（通过模拟store状态）
 */
export function testDifferentUserRoles(routePath = '/backgroundManage') {
    console.log(`\n=== 测试不同用户角色权限 (路由: ${routePath}) ===`);

    const testRoles = [0, 1, 2, 3, 4];
    const roleNames = ['临时用户', '普通用户', '管理员', '超级管理员', '主任'];

    // 导入权限管理器
    import('./RoutePermissionManager.js').then(({ default: RoutePermissionManager }) => {
        const requiredRoles = RoutePermissionManager.getRequiredRoles(routePath);
        console.log(`路由 ${routePath} 需要角色: ${requiredRoles?.join(', ') || '无限制'}`);

        testRoles.forEach((role, index) => {
            // 模拟不同的用户角色
            const originalUser = window.vm?.$store?.state?.user;
            if (window.vm && window.vm.$store && window.vm.$store.state) {
                window.vm.$store.state.user = { ...originalUser, role };
            }

            const hasAccess = RoutePermissionManager.checkRouteAccess(routePath);
            const status = hasAccess ? '✅ 允许' : '❌ 拒绝';
            console.log(`${roleNames[index]}(${role}): ${status}`);

            // 恢复原始用户状态
            if (window.vm && window.vm.$store && window.vm.$store.state) {
                window.vm.$store.state.user = originalUser;
            }
        });
    });
}

/**
 * 测试权限策略的示例
 */
export function demonstratePermissionStrategies() {
    console.log('\n=== 权限策略说明 ===');
    console.log('权限检查支持两种策略：');
    console.log('');
    console.log('1. OR策略（默认）：角色权限 OR 特定权限');
    console.log('   - 用户满足角色要求 或 满足特定权限要求即可访问');
    console.log('   - 例如：管理员角色 或 具有admin权限');
    console.log('');
    console.log('2. AND策略：角色权限 AND 特定权限');
    console.log('   - 用户必须同时满足角色要求 和 特定权限要求');
    console.log('   - 例如：必须是超级管理员 且 具有system_admin权限');
    console.log('');
    console.log('配置示例：');
    console.log('"/backgroundManage": {');
    console.log('    roles: [2, 3],');
    console.log('    permissions: ["admin"],');
    console.log('    strategy: "OR"  // 管理员角色或admin权限任一满足');
    console.log('}');
    console.log('');
    console.log('"/systemManage": {');
    console.log('    roles: [3],');
    console.log('    permissions: ["system_admin"],');
    console.log('    strategy: "AND"  // 必须是超级管理员且有system_admin权限');
    console.log('}');
}

/**
 * 测试新增路由权限的示例
 */
export function demonstrateAddingNewRoute() {
    console.log('\n=== 如何添加新的权限路由示例 ===');
    console.log('现在只需要在一个地方配置权限路由！');
    console.log('');
    console.log('在 RoutePermissionManager.js 的 getRoutePermissionConfig() 方法中添加：');
    console.log('');
    console.log('// 首先导入角色常量');
    console.log('import { USER_ROLE } from "../constants.js";');
    console.log('');
    console.log('static getRoutePermissionConfig() {');
    console.log('    return {');
    console.log('        "/backgroundManage": {');
    console.log('            roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN],');
    console.log('            permissions: ["admin"],');
    console.log('            strategy: "OR"');
    console.log('        },');
    console.log('        "/systemManage": {           // 新增路由');
    console.log('            roles: [USER_ROLE.SUPER_ADMIN],');
    console.log('            permissions: ["system_admin"],');
    console.log('            strategy: "AND"');
    console.log('        },');
    console.log('        "/userManage": {             // 新增路由');
    console.log('            roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN],');
    console.log('            permissions: ["user_admin"],');
    console.log('            strategy: "OR"');
    console.log('        }');
    console.log('    };');
    console.log('}');
    console.log('');
    console.log('✅ 优势：');
    console.log('- 统一配置，避免重复定义');
    console.log('- 使用角色常量，避免魔法值');
    console.log('- 代码更易读和维护');
    console.log('- 所有权限相关方法自动使用新配置');
    console.log('- 路由守卫自动生效');
    console.log('- 减少维护成本和出错概率');
}

/**
 * 测试统一配置的效果
 */
export function testUnifiedConfig() {
    console.log('\n=== 测试统一配置效果 ===');

    // 导入权限管理器
    import('./RoutePermissionManager.js').then(({ default: RoutePermissionManager }) => {
        console.log('当前权限路由配置：');
        const config = RoutePermissionManager.getRoutePermissionConfig();
        console.table(config);

        console.log('\n受保护的路由列表：');
        const protectedRoutes = Object.keys(config);
        protectedRoutes.forEach(route => {
            const isProtected = RoutePermissionManager.isProtectedRoute(route);
            const requiredRoles = RoutePermissionManager.getRequiredRoles(route);
            const routeConfig = RoutePermissionManager.getRouteConfig(route);

            console.log(`${route}:`);
            console.log(`  - 是否受保护: ${isProtected}`);
            console.log(`  - 所需角色: ${requiredRoles?.join(', ') || '无限制'}`);
            console.log(`  - 权限策略: ${routeConfig?.strategy || 'OR'}`);
            console.log(`  - 特定权限: ${routeConfig?.permissions?.join(', ') || '无'}`);
        });
    });
}

/**
 * 运行所有测试
 */
export function runAllTests() {
    console.log('开始权限系统测试...\n');

    // 测试常量
    testUserRoleConstants();

    // 测试权限管理器
    testPermissionManager().then(() => {
        console.log('\n权限管理器测试完成');

        // 测试统一配置
        testUnifiedConfig();

        // 测试路由检查
        simulateRouteCheck('/backgroundManage');

        // 测试不同用户角色
        testDifferentUserRoles('/backgroundManage');

        // 展示权限策略
        demonstratePermissionStrategies();

        // 展示如何添加新路由
        demonstrateAddingNewRoute();

        console.log('\n所有测试完成！');
    }).catch(error => {
        console.error('测试过程中出现错误:', error);
    });
}

// 如果在浏览器环境中，可以通过控制台调用测试
if (typeof window !== 'undefined') {
    window.testPermission = {
        testUserRoleConstants,
        testPermissionManager,
        simulateRouteCheck,
        testDifferentUserRoles,
        testUnifiedConfig,
        demonstratePermissionStrategies,
        demonstrateAddingNewRoute,
        runAllTests
    };

    console.log('权限测试工具已加载，可以通过 window.testPermission 调用测试方法');
}
