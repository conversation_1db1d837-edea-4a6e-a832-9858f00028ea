/**
 * 权限系统测试文件
 * 用于验证后台管理权限是否正确配置
 */

import { USER_ROLE } from '../constants.js';
import permissionManager from './index.js';

/**
 * 测试用户角色常量
 */
export function testUserRoleConstants() {
    console.log('=== 测试用户角色常量 ===');
    console.log('临时用户:', USER_ROLE.TEMP_USER, USER_ROLE[0]);
    console.log('普通用户:', USER_ROLE.NORMAL_USER, USER_ROLE[1]);
    console.log('管理员:', USER_ROLE.ADMIN, USER_ROLE[2]);
    console.log('超级管理员:', USER_ROLE.SUPER_ADMIN, USER_ROLE[3]);
    console.log('主任:', USER_ROLE.DIRECTOR, USER_ROLE[4]);
}

/**
 * 测试权限管理器
 */
export async function testPermissionManager() {
    console.log('=== 测试权限管理器 ===');
    
    // 测试不同角色的用户
    const testUsers = [
        { uid: '1', role: 0, nickname: '临时用户' },
        { uid: '2', role: 1, nickname: '普通用户' },
        { uid: '3', role: 2, nickname: '管理员' },
        { uid: '4', role: 3, nickname: '超级管理员' },
        { uid: '5', role: 4, nickname: '主任' }
    ];
    
    for (const user of testUsers) {
        console.log(`\n--- 测试用户: ${user.nickname} (role: ${user.role}) ---`);
        
        // 初始化权限管理器
        await permissionManager.initialize(user);
        
        // 测试路由权限
        const hasRoutePermission = permissionManager.checkRoutePermission('/backgroundManage');
        console.log(`后台管理路由权限: ${hasRoutePermission}`);
        
        // 测试角色检查
        const isAdmin = permissionManager.isAdmin();
        const isSuperAdmin = permissionManager.isSuperAdmin();
        console.log(`是否为管理员: ${isAdmin}`);
        console.log(`是否为超级管理员: ${isSuperAdmin}`);
        
        // 测试特定权限
        const hasSuperAdminPermission = permissionManager.checkFeaturePermission('super_admin');
        console.log(`超级管理员权限: ${hasSuperAdminPermission}`);
    }
}

/**
 * 模拟路由权限检查
 */
export function simulateRouteCheck(userRole) {
    console.log(`\n=== 模拟路由权限检查 (用户角色: ${userRole}) ===`);
    
    // 模拟路由守卫逻辑
    if (userRole !== 3) {
        console.log('❌ 权限不足，无法访问后台管理');
        console.log('重定向到: /main');
        return false;
    } else {
        console.log('✅ 权限验证通过，允许访问后台管理');
        return true;
    }
}

/**
 * 运行所有测试
 */
export function runAllTests() {
    console.log('开始权限系统测试...\n');
    
    // 测试常量
    testUserRoleConstants();
    
    // 测试权限管理器
    testPermissionManager().then(() => {
        console.log('\n权限管理器测试完成');
        
        // 测试路由检查
        console.log('\n=== 路由权限检查测试 ===');
        [0, 1, 2, 3, 4].forEach(role => {
            simulateRouteCheck(role);
        });
        
        console.log('\n所有测试完成！');
    }).catch(error => {
        console.error('测试过程中出现错误:', error);
    });
}

// 如果在浏览器环境中，可以通过控制台调用测试
if (typeof window !== 'undefined') {
    window.testPermission = {
        testUserRoleConstants,
        testPermissionManager,
        simulateRouteCheck,
        runAllTests
    };
    
    console.log('权限测试工具已加载，可以通过 window.testPermission 调用测试方法');
}
