import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';

/**
 * 主权限管理器单例
 * 统一管理所有子权限管理器，提供统一的权限检查接口
 */
class PermissionManager {
    constructor() {
        if (PermissionManager.instance) {
            return PermissionManager.instance;
        }

        this.routeManager = new RoutePermissionManager();
        this.componentManager = new ComponentPermissionManager();
        this.featureManager = new FeaturePermissionManager();

        this.initialized = false;
        this.userInfo = null;
        this.config = {};

        PermissionManager.instance = this;
    }

    /**
     * 获取单例实例
     * @returns {PermissionManager} 权限管理器实例
     */
    static getInstance() {
        if (!PermissionManager.instance) {
            PermissionManager.instance = new PermissionManager();
        }
        return PermissionManager.instance;
    }

    /**
     * 初始化权限管理器
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        try {
            this.userInfo = userInfo;
            this.config = config;

            // 初始化所有子管理器
            await Promise.all([
                this.routeManager.initialize(userInfo, config),
                this.componentManager.initialize(userInfo, config),
                this.featureManager.initialize(userInfo, config)
            ]);

            this.initialized = true;

            // 触发初始化完成事件
            this.emitEvent('initialized', { userInfo, config });

            console.log('PermissionManager initialized successfully', userInfo);
        } catch (error) {
            console.error('Failed to initialize PermissionManager:', error);
            throw error;
        }
    }

    /**
     * 检查路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(routePath, context = {}) {
        this.ensureInitialized();
        return this.routeManager.hasPermission(routePath, context);
    }

    /**
     * 检查组件权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkComponentPermission(component, action = null, context = {}) {
        this.ensureInitialized();
        return this.componentManager.hasPermission(component, action, context);
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeaturePermission(feature, action = null, context = {}) {
        this.ensureInitialized();
        return this.featureManager.hasPermission(feature, action, context);
    }

    /**
     * 检查API权限
     * @param {string} method - HTTP方法
     * @param {string} path - API路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkApiPermission(method, path, context = {}) {
        this.ensureInitialized();
        return this.featureManager.checkApiPermission(method, path, context);
    }

    /**
     * 检查数据权限
     * @param {string} dataType - 数据类型
     * @param {string} scope - 数据范围
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDataPermission(dataType, scope, context = {}) {
        this.ensureInitialized();
        return this.featureManager.checkDataPermission(dataType, scope, context);
    }

    /**
     * 检查元素权限
     * @param {string} elementSelector - 元素选择器
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkElementPermission(elementSelector, context = {}) {
        this.ensureInitialized();
        return this.componentManager.checkElementPermission(elementSelector, context);
    }

    /**
     * 获取组件可见性
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否可见
     */
    isComponentVisible(component, action = null, context = {}) {
        return this.checkComponentPermission(component, action, context);
    }

    /**
     * 获取组件禁用状态
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否禁用
     */
    isComponentDisabled(component, action = null, context = {}) {
        return !this.checkComponentPermission(component, action, context);
    }

    /**
     * 获取用户可访问的路由列表
     * @returns {Array<string>} 可访问的路由列表
     */
    getAccessibleRoutes() {
        this.ensureInitialized();
        return this.routeManager.getAccessibleRoutes();
    }

    /**
     * 获取用户可执行的功能操作
     * @param {string} feature - 功能名称
     * @returns {Array<string>} 可执行的操作列表
     */
    getAvailableActions(feature) {
        this.ensureInitialized();
        return this.featureManager.getAvailableActions(feature);
    }

    /**
     * 获取重定向路由
     * @param {string} originalRoute - 原始路由
     * @returns {string} 重定向路由
     */
    getRedirectRoute(originalRoute) {
        this.ensureInitialized();
        return this.routeManager.getRedirectRoute(originalRoute);
    }

    /**
     * 批量检查权限
     * @param {Object} permissions - 权限检查配置
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(permissions) {
        this.ensureInitialized();
        const results = {};

        // 检查路由权限
        if (permissions.routes) {
            permissions.routes.forEach(({ route, context = {}, key }) => {
                const permissionKey = key || `route_${route.replace(/\//g, '_')}`;
                results[permissionKey] = this.checkRoutePermission(route, context);
            });
        }

        // 检查组件权限
        if (permissions.components) {
            const componentResults = this.componentManager.batchCheckPermissions(permissions.components);
            Object.assign(results, componentResults);
        }

        // 检查功能权限
        if (permissions.features) {
            const featureResults = this.featureManager.batchCheckPermissions(permissions.features);
            Object.assign(results, featureResults);
        }

        return results;
    }

    /**
     * 更新用户信息
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo };

        // 更新所有子管理器的用户信息
        this.routeManager.updateUserInfo(this.userInfo);
        this.componentManager.updateUserInfo(this.userInfo);
        this.featureManager.updateUserInfo(this.userInfo);

        // 触发用户信息更新事件
        this.emitEvent('userInfoUpdated', this.userInfo);
    }

    /**
     * 清除所有权限缓存
     */
    clearCache() {
        this.routeManager.clearCache();
        this.componentManager.clearCache();
        this.featureManager.clearCache();
    }

    /**
     * 获取当前用户信息
     * @returns {Object} 用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 获取用户角色
     * @returns {number|string} 用户角色
     */
    getUserRole() {
        return this.userInfo?.role || 0;
    }

    /**
     * 获取用户ID
     * @returns {string|number} 用户ID
     */
    getUserId() {
        return this.userInfo?.uid || this.userInfo?.id;
    }

    /**
     * 检查是否为管理员
     * @returns {boolean} 是否为管理员
     */
    isAdmin() {
        const role = this.getUserRole();
        return role === 2 || role === 3 || role === 4; // 管理员、超级管理员、主任
    }

    /**
     * 检查是否为超级管理员
     * @returns {boolean} 是否为超级管理员
     */
    isSuperAdmin() {
        return this.getUserRole() === 3; // 超级管理员角色为3
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 确保已初始化
     */
    ensureInitialized() {
        if (!this.initialized) {
            throw new Error('PermissionManager not initialized. Please call initialize() first.');
        }
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        this.routeManager.destroy();
        this.componentManager.destroy();
        this.featureManager.destroy();

        this.initialized = false;
        this.userInfo = null;
        this.config = {};

        // 清除单例实例
        PermissionManager.instance = null;

        // 触发销毁事件
        this.emitEvent('destroyed');
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {any} data - 事件数据
     */
    emitEvent(eventName, data = null) {
        if (window.vm && window.vm.$emit) {
            window.vm.$emit(`permission:${eventName}`, data);
        }

        // 也可以使用自定义事件
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent(`permission:${eventName}`, { detail: data });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取子管理器
     * @param {string} type - 管理器类型 (route|component|feature)
     * @returns {BasePermissionManager} 子管理器实例
     */
    getManager(type) {
        switch (type) {
        case 'route':
            return this.routeManager;
        case 'component':
            return this.componentManager;
        case 'feature':
            return this.featureManager;
        default:
            throw new Error(`Unknown manager type: ${type}`);
        }
    }
}

// 创建全局实例
const permissionManager = new PermissionManager();

export default permissionManager;
export { PermissionManager };
