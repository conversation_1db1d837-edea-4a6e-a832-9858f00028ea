import BasePermissionManager from './BasePermissionManager.js';

/**
 * 路由权限管理器
 * 负责路由级别的权限控制和导航守卫
 */
class RoutePermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.whiteList = ['/login', '/webLive', '/cloudVideoEditChild', '/pacs_login', '/init']; // 白名单路由
        this.routePermissions = new Map(); // 路由权限配置
        this.roleRouteMap = new Map(); // 角色路由映射
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        // 加载路由权限配置
        this.loadRoutePermissions();
        // 加载角色路由映射
        this.loadRoleRouteMap();
    }

    /**
     * 加载路由权限配置
     */
    loadRoutePermissions() {
        // 基础路由权限配置
        const routePermissions = {
            // 后台管理路由 - 只有管理员和超级管理员可以访问
            '/backgroundManage': { roles: [2, 3], permissions: ['admin'] },
            // 可以在这里添加更多需要权限控制的路由
            // '/systemManage': { roles: [3], permissions: ['super_admin'] },
            // '/userManage': { roles: [2, 3], permissions: ['admin'] },
        };

        // 将配置存储到Map中
        Object.entries(routePermissions).forEach(([route, config]) => {
            this.routePermissions.set(route, config);
        });
    }

    /**
     * 加载角色路由映射
     */
    loadRoleRouteMap() {

    }

    /**
     * 检查路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限访问
     */
    hasPermission(routePath, context = {}) {
        // 检查是否已初始化
        if (!this.isInitialized()) {
            console.warn('RoutePermissionManager not initialized');
            return false;
        }

        // 检查白名单
        if (this.isWhiteListRoute(routePath)) {
            return true;
        }

        // 检查登录状态
        if (!this.isLoggedIn()) {
            return false;
        }

        // 检查具体路由权限
        return this.checkRoutePermission(routePath, context);
    }

    /**
     * 检查是否为白名单路由
     * @param {string} routePath - 路由路径
     * @returns {boolean} 是否为白名单路由
     */
    isWhiteListRoute(routePath) {
        return this.whiteList.some(whitePath => routePath.indexOf(whitePath) > -1);
    }

    /**
     * 检查是否已登录
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        // 检查token
        const token = this.getToken();
        return !!token;
    }

    /**
     * 获取登录token
     * @returns {string} token
     */
    getToken() {
        // 使用Tool.getToken()方法
        if (window.Tool && window.Tool.getToken) {
            return window.Tool.getToken();
        }
        return localStorage.getItem('token') || sessionStorage.getItem('token');
    }

    /**
     * 检查具体路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(routePath, context = {}) {
        // 获取路由权限配置
        const routeConfig = this.getRouteConfig(routePath);

        if (!routeConfig) {
            // 如果没有配置，默认允许访问
            return true;
        }

        // 检查角色权限
        if (routeConfig.roles && routeConfig.roles.length > 0) {
            const userRole = this.getUserRole();
            if (!routeConfig.roles.includes(userRole)) {
                this.logPermissionCheck(routePath, false, { reason: 'role_not_allowed', userRole, requiredRoles: routeConfig.roles });
                return false;
            }
        }

        // 检查特定权限
        if (routeConfig.permissions && routeConfig.permissions.length > 0) {
            const hasRequiredPermissions = routeConfig.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );

            if (!hasRequiredPermissions) {
                this.logPermissionCheck(routePath, false, { reason: 'permission_denied', requiredPermissions: routeConfig.permissions });
                return false;
            }
        }

        this.logPermissionCheck(routePath, true, { routeConfig });
        return true;
    }

    /**
     * 获取路由配置
     * @param {string} routePath - 路由路径
     * @returns {Object|null} 路由配置
     */
    getRouteConfig(routePath) {
        // 精确匹配
        if (this.routePermissions.has(routePath)) {
            return this.routePermissions.get(routePath);
        }

        // 模糊匹配
        for (let [configPath, config] of this.routePermissions) {
            if (routePath.startsWith(configPath)) {
                return config;
            }
        }

        return null;
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        switch (permission) {
        case 'admin':
            return this.isAdmin();
        case 'super_admin':
            return this.isSuperAdmin();
        case 'multicenter_access':
            return this.checkMulticenterAccess(context);
        default:
            return true; // 默认允许
        }
    }

    /**
     * 检查多中心访问权限
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkMulticenterAccess(context = {}) {
        // 基本的多中心访问权限检查
        const userRole = this.getUserRole();
        return userRole >= 1; // 所有登录用户都可以访问多中心
    }

    /**
     * 获取用户可访问的路由列表
     * @returns {Array<string>} 可访问的路由列表
     */
    getAccessibleRoutes() {
        const accessibleRoutes = [];

        // 添加白名单路由
        accessibleRoutes.push(...this.whiteList);

        // 检查配置的路由
        for (let [routePath] of this.routePermissions) {
            if (this.hasPermission(routePath)) {
                accessibleRoutes.push(routePath);
            }
        }

        return accessibleRoutes;
    }

    /**
     * 获取重定向路由
     * @param {string} originalRoute - 原始路由
     * @returns {string} 重定向路由
     */
    getRedirectRoute(originalRoute) {
        // 如果没有登录，重定向到登录页
        if (!this.isLoggedIn()) {
            return '/login';
        }

        // 如果没有权限，重定向到首页或默认页面
        return '/main';
    }

    /**
     * 添加路由权限配置
     * @param {string} routePath - 路由路径
     * @param {Object} config - 权限配置
     */
    addRoutePermission(routePath, config) {
        this.routePermissions.set(routePath, config);
    }

    /**
     * 移除路由权限配置
     * @param {string} routePath - 路由路径
     */
    removeRoutePermission(routePath) {
        this.routePermissions.delete(routePath);
    }

    /**
     * 更新白名单
     * @param {Array<string>} whiteList - 新的白名单
     */
    updateWhiteList(whiteList) {
        this.whiteList = [...whiteList];
    }

    /**
     * 静态方法：检查路由是否需要权限控制
     * @param {string} routePath - 路由路径
     * @returns {boolean} 是否需要权限控制
     */
    static isProtectedRoute(routePath) {
        // 定义需要权限控制的路由列表
        const protectedRoutes = [
            '/backgroundManage',
            // 可以在这里添加更多需要权限控制的路由
            // '/systemManage',
            // '/userManage',
        ];

        return protectedRoutes.some(route => routePath === route || routePath.startsWith(route + '/'));
    }

    /**
     * 静态方法：获取路由所需的角色权限
     * @param {string} routePath - 路由路径
     * @returns {Array<number>|null} 所需角色列表，null表示不需要特殊权限
     */
    static getRequiredRoles(routePath) {
        // 路由权限配置映射
        const routeRoleMap = {
            '/backgroundManage': [2, 3], // 管理员和超级管理员
            // 可以在这里添加更多路由的权限配置
            // '/systemManage': [3], // 只有超级管理员
            // '/userManage': [2, 3], // 管理员和超级管理员
        };

        // 精确匹配
        if (routeRoleMap[routePath]) {
            return routeRoleMap[routePath];
        }

        // 模糊匹配（子路由）
        for (const [route, roles] of Object.entries(routeRoleMap)) {
            if (routePath.startsWith(route + '/')) {
                return roles;
            }
        }

        return null;
    }

    /**
     * 静态方法：获取当前用户角色
     * @returns {number} 用户角色
     */
    static getCurrentUserRole() {
        // 从store中获取用户角色
        if (window.vm && window.vm.$store && window.vm.$store.state.user) {
            return window.vm.$store.state.user.role || 0;
        }
        return 0; // 默认为临时用户
    }

    /**
     * 静态方法：检查用户是否有访问指定路由的权限
     * @param {string} routePath - 路由路径
     * @returns {boolean} 是否有权限
     */
    static checkRouteAccess(routePath) {
        // 检查是否是受保护的路由
        if (!this.isProtectedRoute(routePath)) {
            return true; // 不需要特殊权限的路由，允许访问
        }

        // 获取当前用户角色
        const userRole = this.getCurrentUserRole();

        // 获取所需角色
        const requiredRoles = this.getRequiredRoles(routePath);
        if (!requiredRoles || requiredRoles.length === 0) {
            return true; // 没有角色限制，允许访问
        }

        // 检查用户角色是否在允许的角色列表中
        return requiredRoles.includes(userRole);
    }
}

export default RoutePermissionManager;
